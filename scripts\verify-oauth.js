#!/usr/bin/env node

/**
 * OAuth Configuration Verification Script
 * This script helps verify your Google OAuth setup
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Load environment variables
function loadEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found');
    return {};
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0) {
      env[key.trim()] = valueParts.join('=').trim();
    }
  });
  
  return env;
}

function verifyConfiguration() {
  console.log('🔍 Verifying Google OAuth Configuration...\n');
  
  const env = loadEnvFile();
  
  // Check required environment variables
  const requiredVars = [
    'NEXTAUTH_URL',
    'NEXTAUTH_SECRET',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET'
  ];
  
  let allPresent = true;
  
  requiredVars.forEach(varName => {
    if (env[varName]) {
      console.log(`✅ ${varName}: Present`);
    } else {
      console.log(`❌ ${varName}: Missing`);
      allPresent = false;
    }
  });
  
  if (!allPresent) {
    console.log('\n❌ Some required environment variables are missing.');
    return;
  }
  
  console.log('\n📋 Configuration Summary:');
  console.log(`   NEXTAUTH_URL: ${env.NEXTAUTH_URL}`);
  console.log(`   Google Client ID: ${env.GOOGLE_CLIENT_ID}`);
  
  console.log('\n🔗 Required Redirect URIs for Google Cloud Console:');
  console.log(`   Production: ${env.NEXTAUTH_URL}/api/auth/callback/google`);
  console.log(`   Development: http://localhost:3000/api/auth/callback/google`);
  
  console.log('\n📝 Steps to fix redirect_uri_mismatch error:');
  console.log('1. Go to https://console.cloud.google.com/');
  console.log('2. Navigate to APIs & Services → Credentials');
  console.log(`3. Find OAuth 2.0 Client ID: ${env.GOOGLE_CLIENT_ID}`);
  console.log('4. Add the redirect URIs listed above to "Authorized redirect URIs"');
  console.log('5. Save the configuration');
  console.log('6. Wait a few minutes for changes to propagate');
  
  console.log('\n✅ Configuration verification complete!');
}

verifyConfiguration();
